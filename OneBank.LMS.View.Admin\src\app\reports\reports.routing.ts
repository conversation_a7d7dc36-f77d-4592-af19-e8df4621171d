import { NgModule } from "@angular/core";
import { Routes, RouterModule } from "@angular/router";

const routes: Routes = [
  {
    path: "",
    children: [
      {
        path: "course-progress-report",
        loadChildren: () =>
          import("./course-progress-report/course-progress-report.module").then(
            (module) => module.CourseProgressReportModule
          ),
      },
      {
        path: "grade-summary-report",
        loadChildren: () =>
          import("./grade-summary-report/grade-summary-report.module").then(
            (module) => module.GradeSummaryReportModule
          ),
      },
      {
        path: "trainee-course-history-report",
        loadChildren: () =>
          import(
            "./trainee-course-history-report/trainee-course-history-report.module"
          ).then((module) => module.TraineeCourseHistoryReportModule),
      },
      {
        path: "evaluation-test-progress-report",
        loadChildren: () =>
          import(
            "./evaluation-test-progress-report/evaluation-test-progress-report.module"
          ).then((module) => module.EvalTestProgressReportModule),
      },
      {
        path: "course-wise-enrollment",
        loadChildren: () =>
          import("./course-wise-enrollment/course-wise-enrollment.module").then(
            (module) => module.CourseWiseEnrollmentModule
          ),
      },
      {
        path: "certification-summary",
        loadChildren: () =>
          import("./certification-summary/certification-summary.module").then(
            (module) => module.CertficationSummeryModule
          ),
      },
      // {
      //   path: "user-account-audit-report",
      //   loadChildren: () =>
      //     import(
      //       "./user-account-audit-report/user-account-audit-report.module"
      //     ).then((module) => module.UserAccountAuditReportModule),
      // },
    ],
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class ReportsRoutingModule {}
