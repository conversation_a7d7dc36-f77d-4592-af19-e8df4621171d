import { Component, ViewEncapsulation, OnInit, ViewChild } from "@angular/core";
import { FormBuilder, FormGroup } from "@angular/forms";
import { ToastrService } from "ngx-toastr";
import { BlockUI, NgBlockUI } from "ng-block-ui";
import { BsDaterangepickerConfig } from "ngx-bootstrap/datepicker";
import { environment } from "src/environments/environment";
import { ResponseStatus } from "src/app/_models/enum";
import { CommonService } from "src/app/_services/common.service";
import * as moment from "moment";
import { Validators } from "ngx-editor";

@Component({
  selector: "app-user-account-audit-report",
  templateUrl: "./user-account-audit-report.component.html",
  styleUrls: ["./user-account-audit-report.component.css"],
  encapsulation: ViewEncapsulation.None,
})
export class UserAccountAuditReportComponent implements OnInit {
  @BlockUI() blockUI: NgBlockUI;

  courseList: Array<any> = [];
  divisionList: Array<any> = [];
  url: any;
  pdfClicked = false;
  baseUrl =
    this._service.localBaseUrl !== ""
      ? this._service.localBaseUrl
      : environment.baseUrl;
  filterForm: FormGroup;

  bsConfig: Partial<BsDaterangepickerConfig>;
  batchList: any = [];
  selectedCourse = null;
  loadingIndicator = false;
  @ViewChild("pdfViewerOnDemand", { static: false }) pdfViewerOnDemand: any;
  reportFileName: string;

  constructor(
    public formBuilder: FormBuilder,
    private _service: CommonService,
    private toastr: ToastrService
  ) {}

  ngOnInit() {
    this.bsConfig = Object.assign(
      {},
      {
        maxDate: new Date(),
        containerClass: "theme-blue",
        rangeInputFormat: "DD MMM YYYY",
      }
    );

    this.filterForm = this.formBuilder.group({
      divisionId: [null],
      courseId: [null, [Validators.required]],
      dates: [[]],
      batchId: [null, [Validators.required]],
    });

    this.getCourseList();
    // this.getDivisionList();
  }

  get f() {
    return this.filterForm.controls;
  }

  getCourseList() {
    this._service.get("course/dropdown-list").subscribe(
      (res) => {
        if (res.Status === ResponseStatus.Warning) {
          this.toastr.warning(res.Message, "Warning!", { timeOut: 2000 });
          return;
        } else if (res.Status === ResponseStatus.Error) {
          this.toastr.error(res.Message, "Error!", {
            closeButton: true,
            disableTimeOut: false,
            enableHtml: true,
          });
          return;
        }
        this.courseList = res.Data;
      },
      () => {}
    );
  }

  // getDivisionList() {
  //   this._service.get("division/dropdown-list").subscribe(
  //     (res) => {
  //       if (res.Status === ResponseStatus.Warning) {
  //         this.toastr.warning(res.Message, "Warning!", { timeOut: 2000 });
  //         return;
  //       } else if (res.Status === ResponseStatus.Error) {
  //         this.toastr.error(res.Message, "Error!", {
  //           closeButton: true,
  //           disableTimeOut: false,
  //           enableHtml: true,
  //         });
  //         return;
  //       }

  //       this.divisionList = res.Data;
  //     },
  //     () => { }
  //   );
  // }
  changeCourse(course) {
    if (!course) {
      this.batchList = [];
      return;
    }

    this.selectedCourse = course;
    this.getBatches();
  }

  getBatches() {
    this.batchList = [];
    this._service
      .get("batch/dropdown-list/" + this.selectedCourse.Id)
      .subscribe(
        (res) => {
          if (res.Status === ResponseStatus.Warning) {
            this.toastr.warning(res.Message, "Warning!", { timeOut: 2000 });
            return;
          } else if (res.Status === ResponseStatus.Error) {
            this.toastr.error(res.Message, "Error!", {
              closeButton: true,
              disableTimeOut: false,
              enableHtml: true,
            });
            return;
          }
          this.batchList = res.Data;
          setTimeout(() => {
            this.loadingIndicator = false;
          }, 1000);
        },
        (err) => {}
      );
  }
  showReport(reportType) {
    this.blockUI.start("Generating report. Please wait...");

    // if (!this.filterForm.value.courseId || !this.filterForm.value.batchId)
    //   return;

    const obj = {
      isTrainer: false,
      courseId: this.filterForm.value.courseId,
      batchId: this.filterForm.value.batchId,
      batchCourseId: null, //this param is for trainer report
      divisionId: this.filterForm.value.divisionId,
      startDate:
        this.filterForm.value.dates.length === 2
          ? moment(this.filterForm.value.dates[0]).format("DD-MMM-YYYY")
          : null,
      endDate:
        this.filterForm.value.dates.length === 2
          ? moment(this.filterForm.value.dates[1]).format("DD-MMM-YYYY")
          : null,
      reportType: reportType === "WebView" ? "Pdf" : reportType,
    };

    this._service
      .downloadFile("batch-course/get-user-account-audit-report-excel", obj)
      .subscribe({
        next: (res) => {
          this.blockUI.stop();
          if (reportType === "WebView") {
            this.reportFileName = "User_Account_Audit_Report.pdf";
            this.pdfClicked = true;
            this.url = res;
            return;
          }

          const url = window.URL.createObjectURL(res);
          var link = document.createElement("a");
          link.href = url;
          link.download =
            "User_Account_Audit_Report." +
            (reportType === "Excel" ? "xlsx" : "pdf");
          link.click();
        },
        error: (err) => {
          this.toastr.warning(err.message || err, "Warning!");
          this.blockUI.stop();
        },
        complete: () => this.blockUI.stop(),
      });
  }
}
