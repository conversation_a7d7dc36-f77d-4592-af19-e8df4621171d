<block-ui>
    <div class="row">
        <div class="col-12">
            <div class="card card-border-default">
                <div class="card-header">
                    <h5 class="card-title"> User Account Audit Report</h5>
                </div>

                <div class="card-body">
                    <form [formGroup]="filterForm" class="col-12 custom-inline-form" autocomplete="off">
                        <div class="row">
                            <!-- <div class="mb-3 col-lg-2 col-12">
                                <label class="col-form-label col-form-label-sm text-right">
                                    Division
                                </label>
                                <ng-select *ngIf="divisionList.length > 0" formControlName="divisionId"
                                    [items]="divisionList" bindLabel="Name" class="form-control form-control-sm"
                                    bindValue="Id" placeholder="Select a division">
                                </ng-select>
                            </div> -->

                            <div class="mb-3 col-lg-2 col-12">
                                <label class="col-form-label col-form-label-sm text-right">
                                    Course
                                </label>
                                <ng-select *ngIf="courseList.length > 0" formControlName="courseId" [items]="courseList"
                                    bindLabel="Title" class="form-control form-control-sm" bindValue="Id" (change)="changeCourse($event)"
                                    placeholder="Select a course">
                                </ng-select>
                            </div>
                            <div class="mb-3 col-lg-2 col-12">
                                <label class="col-form-label col-form-label-sm text-right">
                                    Batch
                                </label>
                                <ng-select *ngIf="courseList.length > 0" formControlName="batchId" [items]="batchList"
                                    bindLabel="Title" class="form-control form-control-sm" bindValue="Id"
                                    placeholder="Select a course">
                                </ng-select>
                            </div>
                            <div class="mb-3 col-lg-2 col-12">
                                <label class="col-form-label col-form-label-sm text-right">
                                    Date Range
                                </label>
                                <input type="text" readonly class="form-control form-control-sm" formControlName="dates"
                                    [bsConfig]="bsConfig" bsDaterangepicker placeholder="Select a date range">
                            </div>

                            <div class="mb-3 col-lg-4 col-12">
                                <button  class="btn btn-theme btn-sm me-2"
                                (click)="showReport('WebView')"><i
                                    class="feather icon-eye "></i> Preview </button>
                                <button class="btn btn-theme btn-sm me-2" (click)="showReport('Excel')">
                                    <i class="feather icon-download"></i>
                                    Download
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="col-lg-12 ">
                    <div class="w-100" style="height: 605px"  *ngIf="pdfClicked">
                        <ngx-extended-pdf-viewer [(src)]="url" [zoom]="'page-width'" [showPrintButton]="true"
                        [showRotateButton]="true" [showOpenFileButton]="true"
                        [showSecondaryToolbarButton]="true" [useBrowserLocale]="true">
                      </ngx-extended-pdf-viewer>
                      </div>
                    <!-- <div style=" height: 600px">
                        <ng2-pdfjs-viewer #pdfViewerOnDemand downloadFileName="User_Account_Audit_Report.pdf"
                            [openFile]="false" zoom="100" [viewBookmark]="false" [download]="true">
                        </ng2-pdfjs-viewer>

                    </div> -->
                </div>
            </div>


        </div>
    </div>
</block-ui>
